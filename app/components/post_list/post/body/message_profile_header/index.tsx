// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';
import {View, StyleSheet} from 'react-native';

import ProfilePicture from '@components/profile_picture';
import {observePostAuthor} from '@queries/servers/post';
import {withDatabase, withObservables} from '@nozbe/watermelondb/react';

import type PostModel from '@typings/database/models/servers/post';
import type UserModel from '@typings/database/models/servers/user';
import type {WithDatabaseArgs} from '@typings/database/database';

type MessageProfileHeaderProps = {
    author?: UserModel;
    post: PostModel;
    isCurrentUser: boolean;
    size?: number;
};

const MessageProfileHeader = ({
    author,
    post,
    isCurrentUser,
    size = 32,
}: MessageProfileHeaderProps) => {

    // Show avatar for both current user and others based on time-based grouping
    // Voice messages always show avatars regardless of timing
    if (!author) {
        return null;
    }

    // No restrictions - avatar display is now controlled by parent component
    // based on time-based grouping logic and voice message special case

    const styles = StyleSheet.create({
        container: {
            flexDirection: 'row',
            alignItems: 'center',
            // Positioning is now handled by parent component for top alignment
            zIndex: 100, // Ensure profile picture is above message content
        },
        avatar: {
            // No margin needed since we're only showing avatar
        },
    });

    return (
        <View style={styles.container}>
            <View style={styles.avatar}>
                <ProfilePicture
                    author={author}
                    size={size}
                    showStatus={false}
                    testID={`message_profile_header.${author.id}.profile_picture`}
                />
            </View>
        </View>
    );
};

const withMessageProfileHeader = withObservables(['post'], ({database, post}: {post: PostModel} & WithDatabaseArgs) => {
    const author = observePostAuthor(database, post);

    return {
        author,
    };
});

export default withDatabase(withMessageProfileHeader(MessageProfileHeader));
